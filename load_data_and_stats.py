import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_data_and_analyze(file_path, column_name, sep=','):
    """
    Load data using pandas and provide comprehensive statistics for a specific column
    
    Parameters:
    file_path (str): Path to the CSV file
    column_name (str): Name of the column to analyze
    sep (str): Separator used in the CSV file (default: ',')
    
    Returns:
    dict: Dictionary containing various statistics
    """
    
    try:
        # Load the data
        print(f"Loading data from: {file_path}")
        df = pd.read_csv(file_path, sep=sep)
        print(f"Data loaded successfully! Shape: {df.shape}")
        
        # Display basic info about the dataset
        print("\n" + "="*50)
        print("DATASET OVERVIEW")
        print("="*50)
        print(f"Number of rows: {len(df)}")
        print(f"Number of columns: {len(df.columns)}")
        print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        # Show first few rows
        print("\nFirst 5 rows:")
        print(df.head())
        
        # Check if the specified column exists
        if column_name not in df.columns:
            print(f"\nError: Column '{column_name}' not found in the dataset.")
            print(f"Available columns: {list(df.columns)}")
            return None
        
        # Analyze the specific column
        print(f"\n" + "="*50)
        print(f"ANALYSIS FOR COLUMN: '{column_name}'")
        print("="*50)
        
        column_data = df[column_name]
        
        # Basic information about the column
        print(f"Data type: {column_data.dtype}")
        print(f"Non-null count: {column_data.count()}")
        print(f"Null count: {column_data.isnull().sum()}")
        print(f"Null percentage: {(column_data.isnull().sum() / len(column_data)) * 100:.2f}%")
        
        # Statistics based on data type
        if pd.api.types.is_numeric_dtype(column_data):
            print("\nNUMERICAL STATISTICS:")
            print("-" * 30)
            
            # Descriptive statistics
            stats = column_data.describe()
            print(stats)
            
            # Additional statistics
            print(f"\nAdditional Statistics:")
            print(f"Variance: {column_data.var():.4f}")
            print(f"Standard Deviation: {column_data.std():.4f}")
            print(f"Skewness: {column_data.skew():.4f}")
            print(f"Kurtosis: {column_data.kurtosis():.4f}")
            
            # Quartiles and IQR
            q1 = column_data.quantile(0.25)
            q3 = column_data.quantile(0.75)
            iqr = q3 - q1
            print(f"IQR (Interquartile Range): {iqr:.4f}")
            
            # Outliers detection (using IQR method)
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            outliers = column_data[(column_data < lower_bound) | (column_data > upper_bound)]
            print(f"Number of outliers (IQR method): {len(outliers)}")
            print(f"Outlier percentage: {(len(outliers) / len(column_data)) * 100:.2f}%")
            
            # Create visualization
            plt.figure(figsize=(15, 10))
            
            # Histogram
            plt.subplot(2, 3, 1)
            plt.hist(column_data.dropna(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            plt.title(f'Histogram of {column_name}')
            plt.xlabel(column_name)
            plt.ylabel('Frequency')
            
            # Box plot
            plt.subplot(2, 3, 2)
            plt.boxplot(column_data.dropna())
            plt.title(f'Box Plot of {column_name}')
            plt.ylabel(column_name)
            
            # Q-Q plot
            from scipy import stats
            plt.subplot(2, 3, 3)
            stats.probplot(column_data.dropna(), dist="norm", plot=plt)
            plt.title(f'Q-Q Plot of {column_name}')
            
            # Density plot
            plt.subplot(2, 3, 4)
            column_data.dropna().plot(kind='density', color='red', alpha=0.7)
            plt.title(f'Density Plot of {column_name}')
            plt.xlabel(column_name)
            
            # Cumulative distribution
            plt.subplot(2, 3, 5)
            sorted_data = np.sort(column_data.dropna())
            cumulative = np.arange(1, len(sorted_data) + 1) / len(sorted_data)
            plt.plot(sorted_data, cumulative, marker='.', linestyle='none', alpha=0.7)
            plt.title(f'Cumulative Distribution of {column_name}')
            plt.xlabel(column_name)
            plt.ylabel('Cumulative Probability')
            
            plt.tight_layout()
            plt.show()
            
        else:
            print("\nCATEGORICAL STATISTICS:")
            print("-" * 30)
            
            # Value counts
            value_counts = column_data.value_counts()
            print("Value Counts:")
            print(value_counts.head(10))  # Show top 10 values
            
            print(f"\nUnique values: {column_data.nunique()}")
            print(f"Most frequent value: {column_data.mode().iloc[0] if not column_data.mode().empty else 'N/A'}")
            
            # Create visualization for categorical data
            plt.figure(figsize=(12, 8))
            
            # Bar plot of top categories
            plt.subplot(2, 2, 1)
            top_categories = value_counts.head(10)
            top_categories.plot(kind='bar', color='lightcoral')
            plt.title(f'Top 10 Categories in {column_name}')
            plt.xlabel(column_name)
            plt.ylabel('Count')
            plt.xticks(rotation=45)
            
            # Pie chart of top categories
            plt.subplot(2, 2, 2)
            top_categories.plot(kind='pie', autopct='%1.1f%%', startangle=90)
            plt.title(f'Distribution of Top Categories in {column_name}')
            plt.ylabel('')
            
            plt.tight_layout()
            plt.show()
        
        # Return summary statistics
        summary = {
            'column_name': column_name,
            'data_type': str(column_data.dtype),
            'total_count': len(column_data),
            'non_null_count': column_data.count(),
            'null_count': column_data.isnull().sum(),
            'null_percentage': (column_data.isnull().sum() / len(column_data)) * 100
        }
        
        if pd.api.types.is_numeric_dtype(column_data):
            summary.update({
                'mean': column_data.mean(),
                'median': column_data.median(),
                'std': column_data.std(),
                'min': column_data.min(),
                'max': column_data.max(),
                'unique_values': column_data.nunique()
            })
        else:
            summary.update({
                'unique_values': column_data.nunique(),
                'most_frequent': column_data.mode().iloc[0] if not column_data.mode().empty else None
            })
        
        return summary
        
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return None

# Example usage:
if __name__ == "__main__":
    # Example 1: Load loan data and analyze loan amount
    file_path = "data/Lending Club loan data/loan.csv"
    column_to_analyze = "loan_amnt"  # Change this to the column you want to analyze
    
    print("LOAN DATA ANALYSIS")
    print("=" * 60)
    
    # Load and analyze the data
    results = load_data_and_analyze(file_path, column_to_analyze, sep=',')
    
    if results:
        print(f"\nSUMMARY RESULTS:")
        print("-" * 30)
        for key, value in results.items():
            print(f"{key}: {value}")

# For Jupyter Notebook usage, you can run individual parts:
"""
# In Jupyter Notebook, you can use it like this:

# Load the data
df = pd.read_csv("data/Lending Club loan data/loan.csv", sep=',')

# Analyze a specific column
results = load_data_and_analyze("data/Lending Club loan data/loan.csv", "loan_amnt")

# Or analyze different columns:
# results = load_data_and_analyze("data/Lending Club loan data/loan.csv", "int_rate")
# results = load_data_and_analyze("data/Lending Club loan data/loan.csv", "grade")
# results = load_data_and_analyze("data/Lending Club loan data/loan.csv", "purpose")
"""
