import pandas as pd
import pathlib as pl

# Make sure jupyter lab's current directory is the project root
DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "loan.csv"


chunk_size = 10000
chunks = []

for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):
    chunks.append(chunk)

df = pd.concat(chunks, ignore_index=True)

column_indices = [19, 47, 123, 124, 125, 128, 129, 130, 133, 139, 140, 141]

# Iterate through the indices and print the column name and unique values
for idx in column_indices:
    if idx < len(df.columns):
        column_name = df.columns[idx]
        unique_values = df.iloc[:, idx].unique()
        print(f"Column {idx} - '{column_name}':")
        print(f"Unique values: {unique_values}")
        print("+" + "="*50 + "+")
    else:
        print(f"Column {idx}: Index out of range")

df.shape

# print(df["hardship_start_date"].unique())
# print(df["hardship_end_date"].unique())

columns = ["hardship_flag", "hardship_type", "hardship_reason", "hardship_status", "deferral_term", "hardship_amount", "hardship_start_date", "hardship_end_date", "payment_plan_start_date", "hardship_length", "hardship_dpd", "hardship_loan_status"]

for col in columns:
    unique_values = df[col].unique()
    print(f"Number of unique values: {len(unique_values)}") 
    print(f"Unique values in '{col}': {unique_values}")
    print('-'*50)
    print(f"Count of each unique value:")
    for unique_value in unique_values:
        count = (df[col] == unique_value).sum()
        print(f"{unique_value}: {count}")

    print("+" + "="*50 + "+")

df["loan_status"].unique()

# Let's first examine the problematic columns mentioned in the warning
# Columns (123,124,125,128,129,130,133,139,140,141) have mixed types

# First, let's read just the header to see column names
# header_df = pd.DataFrame(df.iloc[0, :])
header_df = pd.read_csv(DATA_PATH, nrows=0)
print(f"Total columns: {len(header_df.columns)}")
print("\nColumn names at problematic indices:")
problematic_indices = [123, 124, 125, 128, 129, 130, 133, 139, 140, 141]
for idx in problematic_indices:
    if idx < len(header_df.columns):
        print(f"Column {idx}: {header_df.columns[idx]}")
    else:
        print(f"Column {idx}: Index out of range")

# Now let's examine a small sample to see the mixed types
print("\n" + "="*50)
print("EXAMINING MIXED TYPE COLUMNS")
print("="*50)

# Read a small sample to inspect the data types
sample_df = pd.read_csv(DATA_PATH, nrows=1000, low_memory=False)
print(f"\nSample data shape: {sample_df.shape}")

# Check the problematic columns
for idx in problematic_indices:
    if idx < len(sample_df.columns):
        col_name = sample_df.columns[idx]
        print(f"\nColumn {idx} - '{col_name}':")
        print(f"  Data type: {sample_df[col_name].dtype}")
        print(f"  Unique values (first 10): {sample_df[col_name].unique()[:10]}")
        print(f"  Non-null count: {sample_df[col_name].count()}/{len(sample_df)}")