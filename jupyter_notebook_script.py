# Simple script for Jupyter Notebook
# Copy and paste this into your notebook cells

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Cell 1: Load the data
file_path = "data/Lending Club loan data/loan.csv"  # Change this to your file path
df = pd.read_csv(file_path, sep=',')

print(f"Data loaded successfully!")
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")
print("\nFirst 5 rows:")
df.head()

# Cell 2: Basic dataset info
print("Dataset Info:")
print(f"Number of rows: {len(df)}")
print(f"Number of columns: {len(df.columns)}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

print("\nData types:")
print(df.dtypes.value_counts())

print("\nMissing values:")
missing_data = df.isnull().sum()
missing_data = missing_data[missing_data > 0].sort_values(ascending=False)
print(missing_data.head(10))

# Cell 3: Analyze a specific column (change 'loan_amnt' to your desired column)
column_name = 'loan_amnt'  # Change this to the column you want to analyze

if column_name in df.columns:
    column_data = df[column_name]
    
    print(f"Analysis for column: '{column_name}'")
    print(f"Data type: {column_data.dtype}")
    print(f"Non-null count: {column_data.count()}")
    print(f"Null count: {column_data.isnull().sum()}")
    print(f"Null percentage: {(column_data.isnull().sum() / len(column_data)) * 100:.2f}%")
    
    if pd.api.types.is_numeric_dtype(column_data):
        print("\nNumerical Statistics:")
        print(column_data.describe())
        
        print(f"\nAdditional Stats:")
        print(f"Variance: {column_data.var():.4f}")
        print(f"Skewness: {column_data.skew():.4f}")
        print(f"Kurtosis: {column_data.kurtosis():.4f}")
        
    else:
        print("\nCategorical Statistics:")
        print("Value counts:")
        print(column_data.value_counts().head(10))
        print(f"Unique values: {column_data.nunique()}")
else:
    print(f"Column '{column_name}' not found!")

# Cell 4: Visualizations
if column_name in df.columns:
    column_data = df[column_name]
    
    if pd.api.types.is_numeric_dtype(column_data):
        # For numerical columns
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Histogram
        axes[0,0].hist(column_data.dropna(), bins=50, alpha=0.7, color='skyblue')
        axes[0,0].set_title(f'Histogram of {column_name}')
        axes[0,0].set_xlabel(column_name)
        axes[0,0].set_ylabel('Frequency')
        
        # Box plot
        axes[0,1].boxplot(column_data.dropna())
        axes[0,1].set_title(f'Box Plot of {column_name}')
        axes[0,1].set_ylabel(column_name)
        
        # Density plot
        column_data.dropna().plot(kind='density', ax=axes[1,0], color='red', alpha=0.7)
        axes[1,0].set_title(f'Density Plot of {column_name}')
        axes[1,0].set_xlabel(column_name)
        
        # Scatter plot with index (to see patterns over time/order)
        axes[1,1].scatter(range(len(column_data.dropna())), sorted(column_data.dropna()), alpha=0.5)
        axes[1,1].set_title(f'Sorted Values of {column_name}')
        axes[1,1].set_xlabel('Index')
        axes[1,1].set_ylabel(column_name)
        
        plt.tight_layout()
        plt.show()
        
    else:
        # For categorical columns
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Bar plot
        top_categories = column_data.value_counts().head(10)
        top_categories.plot(kind='bar', ax=axes[0], color='lightcoral')
        axes[0].set_title(f'Top 10 Categories in {column_name}')
        axes[0].set_xlabel(column_name)
        axes[0].set_ylabel('Count')
        axes[0].tick_params(axis='x', rotation=45)
        
        # Pie chart
        top_categories.plot(kind='pie', ax=axes[1], autopct='%1.1f%%', startangle=90)
        axes[1].set_title(f'Distribution of {column_name}')
        axes[1].set_ylabel('')
        
        plt.tight_layout()
        plt.show()

# Cell 5: Quick function to analyze any column
def quick_stats(df, column_name):
    """Quick statistics for any column"""
    if column_name not in df.columns:
        print(f"Column '{column_name}' not found!")
        return
    
    col = df[column_name]
    print(f"Column: {column_name}")
    print(f"Type: {col.dtype}")
    print(f"Count: {col.count()}/{len(col)} ({col.count()/len(col)*100:.1f}% non-null)")
    
    if pd.api.types.is_numeric_dtype(col):
        print(f"Mean: {col.mean():.2f}")
        print(f"Median: {col.median():.2f}")
        print(f"Std: {col.std():.2f}")
        print(f"Min: {col.min():.2f}")
        print(f"Max: {col.max():.2f}")
    else:
        print(f"Unique values: {col.nunique()}")
        print(f"Most common: {col.mode().iloc[0] if not col.mode().empty else 'N/A'}")

# Example usage:
# quick_stats(df, 'loan_amnt')
# quick_stats(df, 'grade')
# quick_stats(df, 'purpose')
